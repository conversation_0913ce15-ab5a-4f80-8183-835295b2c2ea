import request from "@/utils/request";

// 查询会议列表
export function listMeeting(query) {
  return request({
    url: "/meeting/list",
    method: "get",
    params: query,
  });
}

// 查询会议详细信息
export function getMeeting(id) {
  return request({
    url: "/meeting/" + id,
    method: "get",
  });
}

// 新增会议
export function addMeeting(data) {
  return request({
    url: "/meeting",
    method: "post",
    data: data,
  });
}

// 修改会议信息
export function updateMeeting(data) {
  return request({
    url: "/meeting",
    method: "put",
    data: data,
  });
}

// 删除会议
export function delMeeting(id) {
  return request({
    url: "/meeting/" + id,
    method: "delete",
  });
}

// 补充会议信息
export function supplementMeeting(data) {
  return request({
    url: "/meetingInfo",
    method: "post",
    data: data,
  });
}

// 获取患者基础信息（已迁移到 patient.js，保留此处为向后兼容）
export function getPatientInfo(patientId) {
  return request({
    url: "/patient/" + patientId,
    method: "get",
  });
}

// 查询专家列表
export function listExperts(query) {
  return request({
    url: "/expert/list",
    method: "get",
    params: query,
  });
}

// 上传会议报告
export function uploadMeetingReport(data) {
  return request({
    url: "/meeting/upload",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 查看会议专家信息
export function getMeetingExperts(meetingId) {
  return request({
    url: "/meeting/seedoctor",
    method: "get",
    params: {
      meetingId: meetingId,
    },
  });
}

// 发起会诊
export function sendMeeting(data) {
  return request({
    url: "/meeting/send",
    method: "post",
    data: data,
  });
}
