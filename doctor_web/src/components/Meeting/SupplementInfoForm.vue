<template>
  <div class="supplement-info-form">
    <div class="supplement-form-header">
      <h3>补充信息</h3>
    </div>
    <div class="form-content">
      <!-- 动态表单区域 -->
      <template v-if="dynamicFields.length > 0">
        <el-form :model="dynamicFormData" ref="dynamicForm" label-width="160px">
          <el-row :gutter="20">
            <el-col v-for="field in sortedDynamicFields" :key="field.id" :span="getFieldSpan(field.formType)">
              <el-form-item :label="field.labelName + '：'" :prop="'field_' + field.id" :required="field.notNull === 1">
                <!-- 动态渲染不同类型的表单组件 -->
                <component
                  :is="getComponentType(field.formType)"
                  v-model="dynamicFormData['field_' + field.id]"
                  v-bind="getComponentProps(field)"
                  :disabled="readonly"
                >
                  <!-- 选项渲染（用于 radio、checkbox） -->
                  <template v-if="needsOptions(field.formType)">
                    <el-radio
                      v-if="field.formType === '3'"
                      v-for="option in getFieldOptions(field.items)"
                      :key="`radio_${field.id}_${option.value}`"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                    <el-checkbox
                      v-if="field.formType === '4'"
                      v-for="option in getFieldOptions(field.items)"
                      :key="`checkbox_${field.id}_${option.value}`"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                    <!-- 级联选择器的选项通过 props 传递，不需要在这里渲染 -->
                  </template>
                </component>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "SupplementInfoForm",
  dicts: ["disease_form_type"],
  props: {
    value: {
      type: [Array, Object],
      default: () => [],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    showTooltip: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 动态表单相关数据
      dynamicFields: [], // 动态字段配置
      dynamicFormData: {}, // 动态表单数据
    };
  },
  computed: {
    // 按排序字段排序的动态字段
    sortedDynamicFields() {
      return [...this.dynamicFields].sort((a, b) => (a.sort || 0) - (b.sort || 0));
    },
  },
  watch: {
    value: {
      handler(newVal) {
        // 处理动态字段数据 - 支持两种格式
        let valuesArray = null;

        if (Array.isArray(newVal)) {
          // 直接传入 values 数组
          valuesArray = newVal;
        } else if (newVal && newVal.values && Array.isArray(newVal.values)) {
          // 传入包含 values 属性的对象
          valuesArray = newVal.values;
        }

        if (valuesArray) {
          this.dynamicFields = valuesArray;
          this.initDynamicFormData();
        }
      },
      immediate: true,
      deep: true,
    },
    dynamicFormData: {
      handler(newVal) {
        // 输出动态表单数据
        this.$emit("input", newVal);
      },
      deep: true,
    },
  },
  methods: {
    // 初始化动态表单数据
    initDynamicFormData() {
      const formData = {};
      this.dynamicFields.forEach((field) => {
        const fieldKey = "field_" + field.id;
        // 使用已有的值或默认值
        formData[fieldKey] = field.labelValue || this.getDefaultValue(field.formType);
      });
      this.dynamicFormData = formData;
    },

    // 根据表单类型获取默认值
    getDefaultValue(formType) {
      switch (formType) {
        case "3": // 单选
          return "";
        case "4": // 多选
          return [];
        case "5": // 时间选择器
          return "";
        case "6": // 级联选择器
          return [];
        case "7": // 上传组件
          return [];
        default: // 文本输入
          return "";
      }
    },

    // 根据表单类型获取组件类型
    getComponentType(formType) {
      const componentMap = {
        1: "el-input", // 单行文本
        2: "el-input", // 多行文本
        3: "el-radio-group", // 单选
        4: "el-checkbox-group", // 多选
        5: "el-date-picker", // 时间选择器
        6: "el-cascader", // 级联选择器
        7: "el-upload", // 上传组件
      };
      return componentMap[formType] || "el-input";
    },

    // 获取组件属性
    getComponentProps(field) {
      const props = {
        placeholder: `请输入${field.labelName}`,
        style: { width: "100%" },
      };

      switch (field.formType) {
        case "2": // 多行文本
          props.type = "textarea";
          props.rows = 3;
          break;
        case "5": // 时间选择器
          props.type = "datetime";
          props.placeholder = `请选择${field.labelName}`;
          props.format = "yyyy-MM-dd HH:mm:ss";
          props.valueFormat = "yyyy-MM-dd HH:mm:ss";
          break;
        case "6": // 级联选择器
          props.placeholder = `请选择${field.labelName}`;
          props.clearable = true;
          props.filterable = true;
          props.options = this.getCascaderOptions(field.items);
          break;
        case "7": // 上传组件
          props.action = "/dev-api/common/upload";
          props.showFileList = true;
          props.multiple = false;
          props.accept = ".jpg,.jpeg,.png,.pdf,.doc,.docx";
          break;
      }

      return props;
    },

    // 判断是否需要选项
    needsOptions(formType) {
      return ["3", "4", "6"].includes(formType);
    },

    // 解析字段选项
    getFieldOptions(items) {
      if (!items) return [];
      return items.split(";").map((item) => ({
        label: item.trim(),
        value: item.trim(),
      }));
    },

    // 解析级联选择器选项
    getCascaderOptions(items) {
      if (!items) return [];
      // 简单的级联选项处理，可以根据实际需求扩展
      return items.split(";").map((item) => ({
        label: item.trim(),
        value: item.trim(),
        children: [], // 可以根据需要添加子选项
      }));
    },

    // 获取字段跨度
    getFieldSpan(formType) {
      switch (formType) {
        case "2": // 多行文本
          return 24;
        case "4": // 多选
          return 24;
        case "7": // 上传组件
          return 24;
        default:
          return 12;
      }
    },

    // 表单验证
    validate() {
      return new Promise((resolve) => {
        if (this.$refs.dynamicForm) {
          this.$refs.dynamicForm.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },

    // 重置表单
    resetForm() {
      if (this.$refs.dynamicForm) {
        this.$refs.dynamicForm.resetFields();
      }
      this.dynamicFormData = {};
    },
  },
};
</script>

<style lang="scss" scoped>
.supplement-info-form {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .supplement-form-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .form-content {
    padding: 20px;
  }

  .upload-section {
    .upload-demo {
      .upload-area {
        width: 100px;
        height: 100px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        .upload-icon {
          font-size: 28px;
          color: #8c939d;
        }
      }
    }

    .file-list-readonly {
      .no-files {
        color: #909399;
        font-style: italic;
      }

      .file-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }
  }
}

::v-deep .el-upload-dragger {
  width: 100px;
  height: 100px;
  border-radius: 6px;
}
</style>
